import jwt from 'jsonwebtoken';
import Employee from '../models/employee.js';

const authMiddleware = async (req, res, next) => {
    try {
        const token = req.cookies.token;
        
        if (!token) {
            return res.status(401).json({
                status: false,
                message: "Unauthorized"
            });
        }
        const decoded = jwt.verify(token, "a-string-secret-at-least-256-bits-long");
        req.employee = await Employee.findById(decoded.id).select('-password');
        next();
    }
    catch (error) {
        res.status(401).json({
            status: false,
            message: "Unauthorized"
        });
    }
}

const roleMiddleware = (roles) => {
    return (req, res, next) => {
        if (!roles.includes(req.employee.role)) {
            return res.status(401).json({
                status: false,
                message: "Unauthorized"
            });
        }
        next();
    }
}

export { authMiddleware, roleMiddleware };