import express from 'express';
import {
    createEmployee,
    getEmployees,
    updateEmployee,
    deleteEmployee
} from '../controllers/employeeController.js';
import {authMiddleware,roleMiddleware} from '../middlewares/authMiddleware.js';

const router = express.Router();

// Employee routes
router.post('/', roleMiddleware(['admin']),createEmployee);
router.get('/', authMiddleware, getEmployees);
router.put('/:id', updateEmployee);
router.delete('/:id', deleteEmployee);

export default router;
