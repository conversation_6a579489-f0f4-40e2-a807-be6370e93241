import Employee from '../models/employee.js';
import { successResponse, errorResponse } from '../utils/responseHelper.js';

// Create a new employee
export const createEmployee = async (req, res) => {
    try {
        const employee = new Employee(req.body);
        const savedEmployee = await employee.save();

        return successResponse(res, 201, 'Employee created successfully', savedEmployee);
    } catch (error) {
        console.error('Error creating employee:', error);
        return errorResponse(res, 500, 'Error creating employee', error.message);
    }
};

// Get all employees
export const getAllEmployees = async (req, res) => {
    try {
        const employees = await Employee.find();
        return successResponse(res, 200, 'Employees retrieved successfully', employees);
    } catch (error) {
        console.error('Error fetching employees:', error);
        return errorResponse(res, 500, 'Error fetching employees', error.message);
    }
};

// Get employee by ID
export const getEmployeeById = async (req, res) => {
    try {
        const { id } = req.params;
        const employee = await Employee.findById(id);

        if (!employee) {
            return errorResponse(res, 404, 'Employee not found');
        }

        return successResponse(res, 200, 'Employee retrieved successfully', employee);
    } catch (error) {
        console.error('Error fetching employee:', error);
        return errorResponse(res, 500, 'Error fetching employee', error.message);
    }
};

// Update employee
export const updateEmployee = async (req, res) => {
    try {
        const { id } = req.params;
        const updatedEmployee = await Employee.findByIdAndUpdate(
            id,
            req.body,
            { new: true, runValidators: true }
        );

        if (!updatedEmployee) {
            return res.status(404).json({
                success: false,
                message: 'Employee not found'
            });
        }

        res.status(200).json({
            success: true,
            message: 'Employee updated successfully',
            data: updatedEmployee
        });
    } catch (error) {
        console.error('Error updating employee:', error);
        res.status(500).json({
            success: false,
            message: 'Error updating employee',
            error: error.message
        });
    }
};

// Delete employee
export const deleteEmployee = async (req, res) => {
    try {
        const { id } = req.params;
        const deletedEmployee = await Employee.findByIdAndDelete(id);

        if (!deletedEmployee) {
            return res.status(404).json({
                success: false,
                message: 'Employee not found'
            });
        }

        res.status(200).json({
            success: true,
            message: 'Employee deleted successfully',
            data: deletedEmployee
        });
    } catch (error) {
        console.error('Error deleting employee:', error);
        res.status(500).json({
            success: false,
            message: 'Error deleting employee',
            error: error.message
        });
    }
};
