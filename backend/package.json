{"name": "backend", "version": "1.0.0", "main": "index.js", "type": "module", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "express": "^5.1.0", "mongodb": "^6.17.0", "mongoose": "^8.16.1", "nodemon": "^3.1.10"}}