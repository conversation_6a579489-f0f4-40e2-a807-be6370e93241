import express from 'express';
import mongoose from 'mongoose';
import Employee from './models/employee.js';

const app = express();
const port = 3001;

app.use(express.json());

const MONGODB_URI = "mongodb+srv://subedirohit49:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0";

const dbConnection = mongoose.connect(MONGODB_URI);

dbConnection.then(()=>{
    console.log('Connected to MongoDB successfully');
}).catch((error)=>{
    console.error('Error connecting to MongoDB:', error);
    process.exit(1);
})


app.post('/employee',async (req,res) => {
    try{

        const employee = new Employee(req.body);
        const savedEmployee = await employee.save();

        res.status(201).json({
            success: true,
            message: 'Employee created successfully',
            data: savedEmployee
        });

    }
    catch (error) {
        console.error('Error creating employee:', error);

        res.status(500).json({
            success: false,
            message: 'Error creating employee',
            error: error.message
        });
    }
})

app.listen(port, () => {
    console.log(`Server listening at http://localhost:${port}`);
})