import express from 'express';
import './config/database.js';
import routes from './routes/index.js';
import cookieParser from 'cookie-parser';

const app = express();
const port = 3001;

// Middleware
app.use(express.json());
app.use(cookieParser());

// Routes
app.use('/api', routes);

// seed admin if not exists
import Employee from './models/employee.js';
import bcrypt from 'bcryptjs';

const seedAdmin = async () => {
    const admin = await Employee.findOne({ email: '<EMAIL>' });
    if (!admin) {
        const hashedPassword = await bcrypt.hash('admin', 10);
        const newAdmin = new Employee({
            name: 'Admin',
            email: '<EMAIL>',
            password: hashedPassword,
            role: 'admin'
        });
        await newAdmin.save();
    }
}

seedAdmin();


app.listen(port, () => {
    console.log(`Server listening at http://localhost:${port}`);
})