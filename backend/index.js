import express from 'express';
import './config/database.js';
import routes from './routes/index.js';
import { errorHandler, notFound } from './middleware/errorHandler.js';

const app = express();
const port = 3001;

// Middleware
app.use(express.json());

// Routes
app.use('/api', routes);

// Error handling middleware (should be last)
app.use(notFound);
app.use(errorHandler);

app.listen(port, () => {
    console.log(`Server listening at http://localhost:${port}`);
})