// Global error handling middleware
export const errorHandler = (err, req, res, next) => {
    console.error('Error:', err);

    // Default error response
    let error = {
        success: false,
        message: err.message || 'Internal Server Error',
        ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
    };

    // Mongoose validation error
    if (err.name === 'ValidationError') {
        const errors = Object.values(err.errors).map(e => e.message);
        error.message = 'Validation Error';
        error.errors = errors;
        return res.status(400).json(error);
    }

    // Mongoose duplicate key error
    if (err.code === 11000) {
        const field = Object.keys(err.keyValue)[0];
        error.message = `${field} already exists`;
        return res.status(400).json(error);
    }

    // Mongoose cast error (invalid ObjectId)
    if (err.name === 'CastError') {
        error.message = 'Invalid ID format';
        return res.status(400).json(error);
    }

    // Default server error
    res.status(err.statusCode || 500).json(error);
};

// 404 handler
export const notFound = (req, res, next) => {
    const error = new Error(`Route ${req.originalUrl} not found`);
    error.statusCode = 404;
    next(error);
};
